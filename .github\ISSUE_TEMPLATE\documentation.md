---
name: 📝 Documentation Improvement
about: Suggest updates or improvements to project documentation.
title: "[Docs] Your suggestion here"
labels: documentation
assignees: ""
---

**Current Issue**
Briefly describe the problem or limitation in the current documentation. Be specific about what is unclear, outdated, or missing.

---

**Proposed Improvement**
Explain how the documentation can be improved. Provide details about the changes you'd like to see, such as:

- Sections that need clarification.
- New topics that should be added.
- Updates to keep documentation accurate.

---

**Affected Pages or Sections**
List the specific pages, files, or sections in the documentation that are impacted. Example:

---

**Additional Context**
Provide any relevant details, such as:

- Links to related issues or pull requests.
- Screenshots or examples of the current documentation.
- External resources or references that could support the improvement.
