#
# AI
#
[ai.openai]
api_key = 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
model = "gpt-4o"

#
# Marketplace
#
[marketplace.facebook]
search_city = 'city'
username = 'username'
password = 'password'
login_wait_time = 60
search_interval = '30m'
max_search_interval = '1h'
seller_locations = ['city', 'surrounding city']
notify = 'user1'
exclude_sellers = []

#
# Users and Notifications
#
[user.user1]
pushbullet_token = 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
email = '<EMAIL>'
notify_with = ['gmail', 'pushover']

[user.user2]
email = '<EMAIL>'
notify_with = 'gmail'

[notification.pushover]
pushover_user_key = 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
pushover_api_token = '<EMAIL>'

[notification.gmail]
smtp_username = '<EMAIL>'
smtp_password = 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'

#
# Items to search
#
[item.name1]
search_phrases = 'search word one'
search_region = 'usa'
search_interval = '1d'
delivery_method = 'shipping'
seller_locations = []

[item.name2]
search_phrases = ['search word one', 'search word two']
description = "it should be from manufacture, the seller should not offer shipping."
keywords = ['search word']
antikeywords = ['exclude word one', 'exclude word two']
notify = 'user2'
search_city = 'another city'
seller_locations = ['another city', 'surrounding city']
exclude_sellers = []
