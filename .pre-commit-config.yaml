---
default_install_hook_types:
  - pre-commit
  - post-checkout
  - post-merge
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-toml
      - id: check-json
      - id: check-yaml
      - id: debug-statements
      - id: check-merge-conflict
      - id: pretty-format-json
        args: [--autofix, "--no-sort-keys"]
        exclude: .ipynb
      - id: end-of-file-fixer
      - id: trailing-whitespace
        exclude: .bumpversion.cfg
  # - repo: https://github.com/timothycrosley/isort
  #   rev: 5.13.2
  #   hooks:
  #     - id: isort
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.7.4
    hooks:
      - id: ruff
        args: [--fix]
  - repo: https://github.com/citation-file-format/cffconvert
    rev: 5295f87c0e261da61a7b919fc754e3a77edd98a7
    hooks:
      - id: validate-cff
  - repo: https://github.com/python-poetry/poetry
    rev: 1.8.3
    hooks:
      - id: poetry-check
      - id: poetry-install
