name: tests

on:
  push:
    branches:
  pull_request:
    branches:

jobs:
  linting:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.2.2

      - name: Set up Python 3.12
        uses: actions/setup-python@v5.6.0
        with:
          python-version: 3.12

      - name: Install system deps
        shell: bash
        run: |
          pip install poetry
          poetry config virtualenvs.in-project true
          poetry install --no-root --only dev --only linters --sync

      - name: Linting
        shell: bash
        run: poetry run pre-commit run --all-files

  tests:
    needs: linting
    name: ${{ matrix.os }} / ${{ matrix.python-version }}
    runs-on: ${{ matrix.os }}-latest
    strategy:
      matrix:
        os: [Ubuntu, MacOS]
        python-version: ["3.10", "3.11", "3.12"]
      fail-fast: true
    steps:
      - uses: actions/checkout@v4.2.2

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5.6.0
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install system deps
        shell: bash
        run: |
          pip install nox-poetry==1.1.0
          pip install poetry==1.8.5
          poetry config virtualenvs.in-project true

      - name: Run mypy with nox
        shell: bash
        run: nox --force-color -s mypy-${{ matrix.python-version }}

      - name: Install Playwright Browsers
        run: |
          pip install playwright
          playwright install --with-deps # Ensures browsers and dependencies are installed

      - name: Run tests with nox
        shell: bash
        run: nox --force-color -s tests-${{ matrix.python-version }}

      - name: Run securtity check
        if: matrix.python-version == '3.12' && matrix.os == 'Ubuntu'
        shell: bash
        run: nox --force-color -s security

      - name: Upload coverage data
        uses: actions/upload-artifact@v4.6.1
        with:
          name: ${{ matrix.os }}-${{ matrix.python-version }}.coverage-data
          path: ".coverage.*"
          include-hidden-files: true
          retention-days: 2

  # coverage:
  #   needs: tests
  #   runs-on: ubuntu-latest
  #   steps:
  #     - uses: actions/checkout@v4.2.2

  #     - name: Set up Python 3.12
  #       uses: actions/setup-python@v5.6.0
  #       with:
  #         python-version: 3.12

  #     - name: Install system deps
  #       shell: bash
  #       run: |
  #         pip install nox-poetry==1.1.0
  #         pip install poetry==1.8.5
  #         poetry config virtualenvs.in-project true

  #     - name: Download coverage data
  #       uses: actions/download-artifact@v4.1.8
  #       with:
  #         pattern: "*.coverage-data"
  #         merge-multiple: true

  #     - name: Create coverage report
  #       shell: bash
  #       run: |
  #         nox --force-color --session=coverage -- --fmt xml

  #     - name: Upload coverage report
  #       uses: codecov/codecov-action@v5.1.2
  #       with:
  #         token: ${{ secrets.CODECOV_TOKEN }}
