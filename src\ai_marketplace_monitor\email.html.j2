<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <style type="text/css">
        /* Base */
        body {
            margin: 0;
            padding: 0;
            min-width: 100%;
            font-family: Arial, sans-serif;
            font-size: 16px;
            line-height: 1.5;
            background-color: #FAFAFA;
            color: #222222;
        }

        /* Layout */
        .wrapper {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background-color: #2C5364;
            padding: 20px;
            text-align: center;
        }

        .content {
            background-color: #FFFFFF;
            padding: 20px;
        }

        .footer {
            background-color: #F5F5F5;
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #666666;
        }

        /* Tables */
        .listing-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .listing-table td {
            padding: 12px;
            border-bottom: 1px solid #EEEEEE;
        }

        /* Typography */
        h1 {
            color: #FFFFFF;
            font-size: 24px;
            margin: 0;
        }

        h2 {
            color: #2C5364;
            font-size: 20px;
            margin: 0 0 20px 0;
        }

        /* Images */
        .listing-image {
            max-width: 100%;
            height: auto;
            margin: 10px 0;
        }

        /* Status Tags */
        .status-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .current-price {
            font-weight: bold;
            margin-right: 8px;
        }

        .original-price {
            color: #666;
            font-size: 0.9em;
        }

        .original-price del {
            text-decoration: line-through;
        }

        .status-new { background-color: #4CAF50; color: white; }
        .status-updated { background-color: #2196F3; color: white; }
        .status-discounted { background-color: #FF9800; color: white; }
        .status-expired { background-color: #F44336; color: white; }
        .status-sent { background-color: #9E9E9E; color: white; }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Header -->
        <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td class="header">
                    <h1>AI Marketplace Monitor</h1>
                </td>
            </tr>
        </table>

        <!-- Content -->
        <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td class="content">
                    <h2>Latest {{ item_name }} Listings</h2>
                    <table class="listing-table" cellpadding="0" cellspacing="0" border="0">
                        {% for listing, rating, ns in listings %}
                        <tr>
                            <td style="padding: 20px;">
                                <!-- Title with status tag -->
                                <div style="margin-bottom: 15px;">
                                    <h3 style="color: #2C5364; font-size: 18px; font-weight: bold; margin: 0; display: inline;">
                                        {{ listing.title }}
                                    </h3>
                                    {% if ns == NotificationStatus.NOT_NOTIFIED %}
                                        <span class="status-tag status-new">NEW</span>
                                    {% elif ns == NotificationStatus.LISTING_CHANGED %}
                                        <span class="status-tag status-updated">UPDATED</span>
                                    {% elif ns == NotificationStatus.LISTING_DISCOUNTED %}
                                        <span class="status-tag status-discounted">DISCOUNTED</span>
                                    {% elif ns == NotificationStatus.EXPIRED %}
                                        <span class="status-tag status-expired">REVISITABLE</span>
                                    {% elif ns == NotificationStatus.NOTIFIED and force %}
                                        <span class="status-tag status-sent">REVISITABLE</span>
                                    {% endif %}
                                </div>

                                <!-- Info rows -->
                                <div style="color: #666666; margin-bottom: 10px;">
                                    <span style="font-weight: bold; color: #333333;">Price:</span>
                                    {% if '|' in listing.price %}
                                        {% set prices = listing.price.split('|') %}
                                        <span class="current-price">{{ prices[0].strip() }}</span>
                                        <span class="original-price"><del>{{ prices[1].strip() }}</del></span>
                                    {% else %}
                                        {{ listing.price }}
                                    {% endif %}
                                </div>
                                <div style="color: #666666; margin-bottom: 15px;">
                                    <span style="font-weight: bold; color: #333333;">Location:</span> {{ listing.location }}
                                </div>

                                <!-- Description -->
                                {% if listing.description %}
                                <div style="color: #444444; margin: 12px 0; line-height: 1.6; font-size: 14px; white-space: pre-line;">
                                    {{ listing.description }}
                                </div>
                                {% endif %}

                                <!-- AI Rating -->
                                {% if rating.comment != rating.NOT_EVALUATED %}
                                <div style="background-color: #F8F9FA; padding: 12px; border-radius: 4px; margin: 15px 0;">
                                    <div style="margin-bottom: 5px;">
                                        <span style="font-weight: bold; color: #333333;">AI Rating:</span>
                                        {{ rating.stars }}
                                    </div>
                                    <div style="color: #666666;">
                                        <em>{{ rating.comment }}</em>
                                    </div>
                                </div>
                                {% else %}
                                <div style="background-color: #F8F9FA; padding: 12px; border-radius: 4px; margin: 15px 0;">
                                    <div style="color: #666666;">
                                        <em>This listing has not been evaluated by AI service for relevance.</em>
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Image -->
                                {% if listing.image and (listing.image|hash in valid_image_hashes) %}
                                    <img src="cid:image_{{ listing.image|hash }}"
                                         alt="{{ listing.title }}"
                                         class="listing-image">
                                {% endif %}

                                <!-- View Listing Button -->
                                <p style="margin: 10px 0;">
                                    <a href="{{ listing.post_url.split('?')[0] }}"
                                       style="background-color: #2C5364; color: white; padding: 10px 20px;
                                              text-decoration: none; border-radius: 4px; display: inline-block;">
                                        View Listing
                                    </a>
                                </p>
                            </td>
                        </tr>
                        {% endfor %}
                    </table>
                </td>
            </tr>
        </table>

        <!-- Footer -->
        <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td class="footer">
                    <p>This is an automated message from <a href="https://github.com/BoPeng/ai-marketplace-monitor">AI Marketplace Monitor</a></p>
                    <p>
                        Brought to you by <a href="https://www.linkedin.com/in/bo-peng-53668026/">Bo Peng</a> from <a href="https://bioworkflows.com/">BioWorkflows.com</a><br>
                        To stop this email, contact the sender
                    </p>
                </td>
            </tr>
        </table>
    </div>
</body>
</html>
