[bumpversion]
commit = True
tag = False
current_version = 0.2.0

[bumpversion:file:pyproject.toml]
search = version = "{current_version}"
replace = version = "{new_version}"

[bumpversion:file:src/ai_marketplace_monitor/__init__.py]
search = __version__ = "{current_version}"
replace = __version__ = "{new_version}"

[bumpversion:file(title):CHANGELOG.md]
search = {#}{#} [Unreleased]
replace = {#}{#} [Unreleased]

	{#}{#} [{new_version}] - {now:%Y-%m-%d}

[bumpversion:file(links):CHANGELOG.md]
search = [Unreleased]: https://github.com/BoPeng/ai-marketplace-monitor/compare/v{current_version}...HEAD
replace = [Unreleased]: https://github.com/BoPeng/ai-marketplace-monitor/compare/v{new_version}...HEAD
	[{new_version}]: https://github.com/BoPeng/ai-marketplace-monitor/compare/v{current_version}...v{new_version}

[bumpversion:file(version):CITATION.cff]
search = version: {current_version}
replace = version: {new_version}

[bumpversion:file(tag):CITATION.cff]
search = https://github.com/BoPeng/ai-marketplace-monitor/releases/tag/v{current_version}
replace = https://github.com/BoPeng/ai-marketplace-monitor/releases/tag/v{new_version}

[bumpversion:file(description):CITATION.cff]
search = description: The Software Heritage link for version {current_version}.
replace = description: The Software Heritage link for version {new_version}.
