[tool.poetry]
name = "ai-marketplace-monitor"
version = "0.9.5"
description = "An AI-based tool for monitoring facebook marketplace"
authors = ["<PERSON>g <<EMAIL>>"]
readme = "README.md"
homepage = "https://github.com/BoPeng/ai-marketplace-monitor"
repository = "https://github.com/BoPeng/ai-marketplace-monitor"
documentation = "https://ai-marketplace-monitor.readthedocs.io"
keywords = ["ai-marketplace-monitor"]
classifiers = [
  "Development Status :: 2 - Pre-Alpha",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: GNU Affero General Public License v3",
  "Natural Language :: English",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
]

[tool.poetry.urls]
"Bug Tracker" = "https://github.com/BoPeng/ai-marketplace-monitor/issues"

[tool.poetry.scripts]
ai-marketplace-monitor = 'ai_marketplace_monitor.cli:app'

[tool.poetry.dependencies]
python = ">=3.10"
typer = { extras = ["all"], version = ">=0.15.1,<0.17.0" }
playwright = ">=1.41.0"
rich = ">=13.7.0"
"pushbullet.py" = ">=0.12.0"
diskcache = ">=5.6.3"
watchdog = ">=4.0.0"
openai = ">=1.24.0"
parsedatetime = ">=2.5"
humanize = ">=4.0.0"
schedule = ">=1.2.2"
inflect = ">=7.0.0"
pynput = ">=1.7.0"
pillow = ">=10.0.0"
jinja2 = ">=3.0.0"
pyparsing = ">=3.1.0"
requests = ">=2.30.0"
CurrencyConverter = ">=0.18.0"
tomli = { version = "2.2.1", markers = "python_version < '3.11'" }

[tool.poetry.group.dev.dependencies]
pre-commit = "^4.0.1"
invoke = "^2.2.0"
bump2version = "^1.0.1"
watchdog = { version = "^6.0.0", extras = ["watchmedo"] }

[tool.poetry.group.test.dependencies]
pytest = "^8.3.3"
xdoctest = "^1.2.0"
coverage = { version = "^7.6.7", extras = ["toml"] }
pytest-cov = "^6.0.0"
pytest-playwright = "^0.7.0"

[tool.poetry.group.linters.dependencies]
isort = ">=5.13.2,<7.0.0"
black = ">=24.10,<26.0"
ruff = ">=0.9.2,<0.13.0"

[tool.poetry.group.security.dependencies]
safety = "^3.2.11"

[tool.poetry.group.typing.dependencies]
mypy = "^1.13.0"

[tool.poetry.group.docs.dependencies]
sphinx = "^8.1.3"
recommonmark = "^0.7.1"

[tool.coverage.paths]
source = ["src", "*/site-packages"]

[tool.coverage.run]
branch = true
source = ["ai_marketplace_monitor"]

[tool.coverage.report]
fail_under = 100
exclude_lines = [
  "pragma: no cover",
  "def __repr__",
  "if self.debug",
  "if settings.DEBUG:",
  "raise AssertionError",
  "raise NotImplementedError",
  "if 0:",
  "if __name__ == __main__:",
]
show_missing = true

[tool.coverage.html]
directory = "htmlcov"

[tool.ruff]
target-version = "py39"
output-format = "full"
line-length = 99
fix = true
extend-exclude = ["docs/*"]

[tool.ruff.lint]
ignore = [
  "ANN202", # **kwargs: Any
  "ANN401",
  "ANN002",
  "ANN003",
  "E722",
  "G004",
  "S311",
  "B017",
  "S106",
  "G003",
  "S101",   # use of assert
  "BLE001", # blank exception
  "C901",   # too complex (function name too long etc)
  "D100",   # docstring
  "D101",   # docstring
  "D102",   # docstring
  "D103",   # docstring
  "D107",   # docstring
  "D415",   # docstring
  "ERA001", # commented code
  "S108",   # use of /tmp
  "S603",   # subprocess.run security
  "S607",   # subprocess.run with relative path
  "E501",   # line too long
  "S112",   # logging try/except/continue
]
select = [
  "E",
  "F",
  "W",   # flake8
  "C",   # mccabe
  "I",   # isort
  "N",   # pep8-naming
  "D",   # flake8-docstrings
  "ANN", # flake8-annotations
  "S",   # flake8-bandit
  "BLE", # flake8-blind-except
  "B",   # flake8-bugbear
  "A",   # flake8-builtins
  "G",   # flake8-logging-format
  "ERA", # eradicate
  "ISC", # flake8-implicit-str-concat
  "RUF", # Ruff-specific rules
]

unfixable = [
  "ERA", # Don't remove commented-out code
]

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["S101"]

[tool.ruff.lint.mccabe]
max-complexity = 10

[tool.ruff.lint.isort]
known-first-party = ["ai_marketplace_monitor"]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.isort]
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
line_length = 99
known_third_party = ["invoke", "nox", "nox_poetry"]

[tool.black]
line-length = 99
target-version = ["py39"]

[tool.mypy]
warn_return_any = false
warn_unused_configs = true

[[tool.mypy.overrides]]
module = ["pytest.*", "invoke.*", "nox.*", "nox_poetry.*"]
allow_redefinition = false
check_untyped_defs = true
ignore_errors = false
ignore_missing_imports = true
implicit_reexport = true
local_partial_types = true
strict_optional = true
strict_equality = true
no_implicit_optional = true
warn_unused_ignores = true
warn_unreachable = true
warn_no_return = true

[build-system]
requires = ["poetry>=0.12"]
build-backend = "poetry.masonry.api"
